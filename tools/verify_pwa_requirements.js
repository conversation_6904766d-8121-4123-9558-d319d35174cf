// PWA Requirements Verification Script
// Run this in browser console to verify PWA setup

console.log('🔍 Verifying PWA Requirements for KFT Fitness...\n');

const requirements = {
    manifest: false,
    serviceWorker: false,
    https: false,
    icons: false,
    startUrl: false,
    display: false,
    themeColor: false
};

// Check 1: Manifest file
async function checkManifest() {
    try {
        const response = await fetch('/manifest.json');
        if (response.ok) {
            const manifest = await response.json();
            requirements.manifest = true;
            console.log('✅ Manifest file found');
            
            // Check manifest properties
            if (manifest.name && manifest.short_name) {
                console.log('✅ App name and short name defined');
            }
            
            if (manifest.start_url) {
                requirements.startUrl = true;
                console.log('✅ Start URL defined:', manifest.start_url);
            }
            
            if (manifest.display) {
                requirements.display = true;
                console.log('✅ Display mode defined:', manifest.display);
            }
            
            if (manifest.theme_color) {
                requirements.themeColor = true;
                console.log('✅ Theme color defined:', manifest.theme_color);
            }
            
            if (manifest.icons && manifest.icons.length > 0) {
                requirements.icons = true;
                console.log('✅ Icons defined:', manifest.icons.length, 'icons');
                
                // Check for required icon sizes
                const has192 = manifest.icons.some(icon => icon.sizes.includes('192x192'));
                const has512 = manifest.icons.some(icon => icon.sizes.includes('512x512'));
                
                if (has192 && has512) {
                    console.log('✅ Required icon sizes (192x192, 512x512) present');
                } else {
                    console.log('⚠️ Missing required icon sizes');
                }
            }
            
        } else {
            console.log('❌ Manifest file not found');
        }
    } catch (error) {
        console.log('❌ Error checking manifest:', error.message);
    }
}

// Check 2: Service Worker
async function checkServiceWorker() {
    if ('serviceWorker' in navigator) {
        try {
            const registration = await navigator.serviceWorker.getRegistration();
            if (registration) {
                requirements.serviceWorker = true;
                console.log('✅ Service Worker registered');
                console.log('   Scope:', registration.scope);
                console.log('   State:', registration.active?.state || 'unknown');
            } else {
                console.log('❌ Service Worker not registered');
            }
        } catch (error) {
            console.log('❌ Error checking Service Worker:', error.message);
        }
    } else {
        console.log('❌ Service Worker not supported');
    }
}

// Check 3: HTTPS or localhost
function checkHTTPS() {
    const isSecure = location.protocol === 'https:' || 
                    location.hostname === 'localhost' || 
                    location.hostname === '127.0.0.1';
    
    if (isSecure) {
        requirements.https = true;
        console.log('✅ Secure context (HTTPS or localhost)');
    } else {
        console.log('❌ Not a secure context - PWA requires HTTPS');
    }
}

// Check 4: Install prompt availability
function checkInstallPrompt() {
    if (window.deferredPrompt || window.matchMedia('(display-mode: standalone)').matches) {
        console.log('✅ PWA install prompt available or app already installed');
    } else {
        console.log('⚠️ Install prompt not available (may appear after user engagement)');
    }
}

// Check 5: PWA capabilities
function checkPWACapabilities() {
    const capabilities = [];
    
    if ('serviceWorker' in navigator) capabilities.push('Service Worker');
    if ('PushManager' in window) capabilities.push('Push Notifications');
    if ('caches' in window) capabilities.push('Cache API');
    if ('Notification' in window) capabilities.push('Notifications');
    if (navigator.onLine !== undefined) capabilities.push('Online/Offline Detection');
    
    console.log('✅ PWA Capabilities:', capabilities.join(', '));
}

// Check 6: Performance metrics
function checkPerformance() {
    if ('performance' in window && performance.timing) {
        const timing = performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        console.log('📊 Page load time:', loadTime + 'ms');
        
        if (loadTime < 3000) {
            console.log('✅ Good load performance (< 3s)');
        } else {
            console.log('⚠️ Slow load performance (> 3s)');
        }
    }
}

// Run all checks
async function runAllChecks() {
    console.log('🚀 Starting PWA verification...\n');
    
    checkHTTPS();
    await checkManifest();
    await checkServiceWorker();
    checkInstallPrompt();
    checkPWACapabilities();
    checkPerformance();
    
    // Summary
    console.log('\n📋 PWA Requirements Summary:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const passed = Object.values(requirements).filter(Boolean).length;
    const total = Object.keys(requirements).length;
    
    Object.entries(requirements).forEach(([requirement, passed]) => {
        console.log(`${passed ? '✅' : '❌'} ${requirement}`);
    });
    
    console.log(`\n🎯 Score: ${passed}/${total} requirements met`);
    
    if (passed === total) {
        console.log('🎉 All PWA requirements satisfied! Your app is PWA-ready.');
    } else {
        console.log('⚠️ Some requirements need attention for full PWA compliance.');
    }
    
    // Installation guidance
    console.log('\n📱 Installation Instructions:');
    console.log('1. Open this app in Chrome/Edge on mobile or desktop');
    console.log('2. Look for "Install" or "Add to Home Screen" option');
    console.log('3. Follow the browser prompts to install');
    console.log('4. The app will appear on your home screen/app drawer');
}

// Auto-run checks
runAllChecks();

// Export for manual use
window.verifyPWA = runAllChecks;
