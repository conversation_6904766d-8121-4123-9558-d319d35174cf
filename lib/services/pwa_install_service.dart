import 'dart:async';
import 'dart:html' as html;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service to manage PWA installation prompts and functionality
/// Handles beforeinstallprompt event and provides installation capabilities
class PWAInstallService {
  static final PWAInstallService _instance = PWAInstallService._internal();
  factory PWAInstallService() => _instance;
  PWAInstallService._internal();

  // Event and state management
  html.Event? _beforeInstallPromptEvent;
  bool _isInstallable = false;
  bool _isInstalled = false;
  bool _hasUserDismissedPermanently = false;
  
  // Stream controllers for reactive updates
  final StreamController<bool> _installabilityController = StreamController<bool>.broadcast();
  final StreamController<bool> _installationController = StreamController<bool>.broadcast();
  
  // Streams for listening to changes
  Stream<bool> get installabilityStream => _installabilityController.stream;
  Stream<bool> get installationStream => _installationController.stream;
  
  // Getters
  bool get isInstallable => _isInstallable && !_isInstalled && !_hasUserDismissedPermanently;
  bool get isInstalled => _isInstalled;
  bool get hasUserDismissedPermanently => _hasUserDismissedPermanently;

  /// Initialize the PWA install service
  Future<void> initialize() async {
    if (!kIsWeb) return;

    try {
      debugPrint('🔧 Initializing PWA Install Service...');

      // Load user preferences
      await _loadUserPreferences();

      // Check if already installed
      await _checkInstallationStatus();

      // Set up event listeners IMMEDIATELY
      _setupEventListeners();

      // Set installable to true by default on web since we have proper PWA setup
      if (!_isInstalled && !_hasUserDismissedPermanently) {
        _isInstallable = true;
        _installabilityController.add(_isInstallable);
        debugPrint('✅ Set PWA as installable by default');
      }

      // Force immediate check for installability
      await Future.delayed(const Duration(milliseconds: 100));
      _forceInstallabilityCheck();

      debugPrint('✅ PWA Install Service initialized');
      debugPrint('📱 Is installable: $_isInstallable');
      debugPrint('📲 Is installed: $_isInstalled');
      debugPrint('🚫 User dismissed permanently: $_hasUserDismissedPermanently');

    } catch (e) {
      debugPrint('❌ Error initializing PWA Install Service: $e');
    }
  }

  /// Set up event listeners for PWA installation
  void _setupEventListeners() {
    try {
      debugPrint('🎧 Setting up PWA event listeners...');

      // Listen for beforeinstallprompt event with new handler
      html.window.addEventListener('beforeinstallprompt', _handleBeforeInstallPrompt);

      // Listen for appinstalled event
      html.window.addEventListener('appinstalled', (event) {
        debugPrint('🎉 PWA has been installed!');
        _isInstalled = true;
        _isInstallable = false;
        _beforeInstallPromptEvent = null;

        _installationController.add(_isInstalled);
        _installabilityController.add(_isInstallable);

        _saveInstallationStatus(true);
      });

      // Check for display mode changes (indicates installation)
      html.window.addEventListener('DOMContentLoaded', (event) {
        _checkDisplayMode();
      });

      debugPrint('✅ PWA event listeners set up successfully');

    } catch (e) {
      debugPrint('❌ Error setting up PWA event listeners: $e');
    }
  }

  /// Check if the app is running in standalone mode (installed)
  void _checkDisplayMode() {
    try {
      final mediaQuery = html.window.matchMedia('(display-mode: standalone)');
      final isStandalone = mediaQuery.matches;
      
      if (isStandalone && !_isInstalled) {
        debugPrint('📱 App is running in standalone mode - marking as installed');
        _isInstalled = true;
        _installationController.add(_isInstalled);
        _saveInstallationStatus(true);
      }
    } catch (e) {
      debugPrint('❌ Error checking display mode: $e');
    }
  }

  /// Check current installation status
  Future<void> _checkInstallationStatus() async {
    try {
      // Check if running in standalone mode
      _checkDisplayMode();
      
      // Check saved installation status
      final prefs = await SharedPreferences.getInstance();
      final savedInstallStatus = prefs.getBool('pwa_installed') ?? false;
      
      if (savedInstallStatus && !_isInstalled) {
        _isInstalled = true;
        _installationController.add(_isInstalled);
      }
      
    } catch (e) {
      debugPrint('❌ Error checking installation status: $e');
    }
  }

  /// Load user preferences regarding PWA installation
  Future<void> _loadUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _hasUserDismissedPermanently = prefs.getBool('pwa_dismissed_permanently') ?? false;
      
      debugPrint('📋 Loaded user preferences - dismissed permanently: $_hasUserDismissedPermanently');
    } catch (e) {
      debugPrint('❌ Error loading user preferences: $e');
    }
  }

  /// Save installation status
  Future<void> _saveInstallationStatus(bool installed) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('pwa_installed', installed);
      debugPrint('💾 Saved installation status: $installed');
    } catch (e) {
      debugPrint('❌ Error saving installation status: $e');
    }
  }

  /// Prompt user to install the PWA
  Future<bool> promptInstall() async {
    if (!kIsWeb || _beforeInstallPromptEvent == null || _isInstalled) {
      debugPrint('⚠️ Cannot prompt install - not available');
      return false;
    }
    
    try {
      debugPrint('📱 Prompting user to install PWA...');
      // Trigger the install prompt
      final dynamic prompt = _beforeInstallPromptEvent;
      final result = await prompt.prompt();
      _beforeInstallPromptEvent = null; // Prevent repeated prompt calls
      debugPrint('📋 User choice: [${result.outcome}');
      if (result.outcome == 'accepted') {
        debugPrint('🎉 User accepted PWA installation');
        return true;
      } else {
        debugPrint('❌ User declined PWA installation');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Error prompting PWA install: $e');
      _beforeInstallPromptEvent = null; // Also clear on error
      return false;
    }
  }

  /// Mark that user has dismissed the install prompt permanently
  Future<void> dismissPermanently() async {
    try {
      _hasUserDismissedPermanently = true;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('pwa_dismissed_permanently', true);
      
      debugPrint('🚫 User dismissed PWA install permanently');
    } catch (e) {
      debugPrint('❌ Error saving permanent dismissal: $e');
    }
  }

  /// Reset permanent dismissal (for testing or user preference change)
  Future<void> resetDismissal() async {
    try {
      _hasUserDismissedPermanently = false;
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('pwa_dismissed_permanently', false);
      
      debugPrint('🔄 Reset PWA install dismissal');
    } catch (e) {
      debugPrint('❌ Error resetting dismissal: $e');
    }
  }

  /// Check if we should show the install prompt
  bool shouldShowInstallPrompt() {
    // More aggressive approach - show prompt if we're on web and not installed
    // even if beforeinstallprompt event hasn't fired yet
    final shouldShow = kIsWeb &&
           !_isInstalled &&
           !_hasUserDismissedPermanently &&
           (_beforeInstallPromptEvent != null || _isInstallable);

    debugPrint('🔍 PWA shouldShowInstallPrompt: $shouldShow (installable: $_isInstallable, installed: $_isInstalled, dismissed: $_hasUserDismissedPermanently, hasEvent: ${_beforeInstallPromptEvent != null})');
    return shouldShow;
  }

  /// Force check for installability (useful for immediate prompts)
  void forceCheckInstallability() {
    if (!kIsWeb) return;

    debugPrint('🔄 Force checking PWA installability...');

    // Check display mode
    _checkDisplayMode();

    // Trigger installability check
    _installabilityController.add(_isInstallable);

    debugPrint('📊 Current state - installable: $_isInstallable, installed: $_isInstalled, hasEvent: ${_beforeInstallPromptEvent != null}');
  }

  /// Force installability check with more aggressive approach
  void _forceInstallabilityCheck() {
    if (!kIsWeb) return;

    debugPrint('🚀 Forcing aggressive installability check...');

    // Check if we're in a PWA-capable environment
    final isHttps = html.window.location.protocol == 'https:' ||
                   html.window.location.hostname == 'localhost' ||
                   html.window.location.hostname == '127.0.0.1';

    debugPrint('🔒 HTTPS/Localhost check: $isHttps');

    if (isHttps) {
      // Check if service worker is available
      if (html.window.navigator.serviceWorker != null) {
        debugPrint('🔧 Service Worker API available');

        // Try to get service worker registrations
        html.window.navigator.serviceWorker!.getRegistrations().then((registrations) {
          debugPrint('📋 Service Worker registrations: ${registrations.length}');

          if (registrations.isNotEmpty) {
            debugPrint('✅ Service Worker registered - PWA requirements likely met');

            // If we have SW but no install event yet, keep checking
            if (_beforeInstallPromptEvent == null) {
              debugPrint('⏳ Waiting for beforeinstallprompt event...');
              _startAggressiveEventListening();
            }
          } else {
            debugPrint('⚠️ No Service Worker registrations found');
          }
        }).catchError((error) {
          debugPrint('❌ Error checking Service Worker: $error');
        });
      } else {
        debugPrint('❌ Service Worker API not available');
      }
    } else {
      debugPrint('❌ PWA requires HTTPS or localhost');
    }
  }

  /// Start aggressive event listening for beforeinstallprompt
  void _startAggressiveEventListening() {
    if (!kIsWeb) return;

    debugPrint('👂 Starting aggressive event listening...');

    // Set up multiple listeners to catch the event
    html.window.addEventListener('beforeinstallprompt', (event) {
      debugPrint('🎯 CAUGHT beforeinstallprompt event via addEventListener!');
      _handleBeforeInstallPrompt(event);
    });

    // Check periodically if the event becomes available
    int attempts = 0;
    const maxAttempts = 100; // 20 seconds

    void checkPeriodically() {
      if (attempts >= maxAttempts || _beforeInstallPromptEvent != null) {
        if (attempts >= maxAttempts) {
          debugPrint('⏰ Stopped checking for beforeinstallprompt after 20 seconds');
        }
        return;
      }

      attempts++;

      // Log every 25 attempts (5 seconds)
      if (attempts % 25 == 0) {
        debugPrint('🔍 Still waiting for beforeinstallprompt... (${attempts * 200}ms elapsed)');
      }

      Future.delayed(const Duration(milliseconds: 200), checkPeriodically);
    }

    checkPeriodically();
  }

  /// Handle beforeinstallprompt event
  void _handleBeforeInstallPrompt(html.Event event) {
    debugPrint('📱 Processing beforeinstallprompt event');
    event.preventDefault(); // Prevent default browser prompt

    _beforeInstallPromptEvent = event;
    _isInstallable = true;
    _installabilityController.add(_isInstallable);

    debugPrint('✅ PWA installation prompt is ready!');
    debugPrint('🎉 beforeinstallprompt event captured successfully');
  }

  /// Manually trigger install prompt (for testing or when beforeinstallprompt doesn't fire)
  Future<bool> manualTriggerInstall() async {
    if (!kIsWeb || _isInstalled) {
      debugPrint('⚠️ Cannot manually trigger install - not available');
      return false;
    }
    
    try {
      debugPrint('📱 Manually triggering PWA install...');
      
      // Try to use the stored event if available
      if (_beforeInstallPromptEvent != null) {
        return await promptInstall();
      } else {
        // If no event, we can still show the dialog and let the user try to install
        debugPrint('📱 No beforeinstallprompt event, but showing install dialog anyway');
        return true; // Return true to indicate we can show the dialog
      }
      
    } catch (e) {
      debugPrint('❌ Error manually triggering PWA install: $e');
      return false;
    }
  }

  /// Get installation statistics for analytics
  Map<String, dynamic> getInstallationStats() {
    return {
      'is_web': kIsWeb,
      'is_installable': _isInstallable,
      'is_installed': _isInstalled,
      'has_dismissed_permanently': _hasUserDismissedPermanently,
      'should_show_prompt': shouldShowInstallPrompt(),
      'has_install_event': _beforeInstallPromptEvent != null,
    };
  }

  /// Dispose of resources
  void dispose() {
    _installabilityController.close();
    _installationController.close();
  }
}
