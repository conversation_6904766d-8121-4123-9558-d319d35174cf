import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/simple_pwa_service.dart';
import 'simple_pwa_dialog.dart';

/// Simple PWA prompt widget that shows install dialog once on page load
/// Provides a clean, professional approach to PWA installation
class SimplePWAPrompt extends StatefulWidget {
  final Widget child;

  const SimplePWAPrompt({
    Key? key,
    required this.child,
  }) : super(key: key);

  @override
  State<SimplePWAPrompt> createState() => _SimplePWAPromptState();
}

class _SimplePWAPromptState extends State<SimplePWAPrompt> {
  final SimplePWAService _pwaService = SimplePWAService();
  bool _hasCheckedPrompt = false;

  @override
  void initState() {
    super.initState();
    if (kIsWeb) {
      _initializePWAPrompt();
    }
  }

  Future<void> _initializePWAPrompt() async {
    // Wait for the widget to be built and the page to be loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowPrompt();
    });

    // Also listen for installability changes
    _pwaService.installabilityStream.listen((isInstallable) {
      if (isInstallable && !_hasCheckedPrompt && mounted) {
        _checkAndShowPrompt();
      }
    });
  }

  void _checkAndShowPrompt() {
    if (!mounted || _hasCheckedPrompt || !kIsWeb) return;

    _hasCheckedPrompt = true;

    // Check if we should show the prompt
    if (_pwaService.shouldShowPrompt) {
      // Show prompt after a brief delay to ensure smooth page load
      Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          _showInstallDialog();
        }
      });
    }
  }

  Future<void> _showInstallDialog() async {
    if (!mounted) return;

    debugPrint('🚀 Showing simple PWA install prompt');

    try {
      await SimplePWADialog.show(
        context,
        onInstallSuccess: () {
          debugPrint('🎉 PWA installed successfully!');
        },
        onDismiss: () {
          debugPrint('📱 PWA install prompt dismissed');
        },
      );
    } catch (e) {
      debugPrint('❌ Error showing PWA dialog: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
