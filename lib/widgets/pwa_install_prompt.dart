import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/pwa_install_service.dart';
import '../widgets/pwa_install_dialog.dart';
import '../design_system/kft_design_system.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Widget that manages PWA installation prompts
/// Shows prompts immediately when Flutter page loads
class PWAInstallPrompt extends StatefulWidget {
  final Widget child;
  final bool showOnAppLaunch;
  final bool showOnHomePage;
  final Duration delayBeforePrompt;
  final bool showImmediately;

  const PWAInstallPrompt({
    Key? key,
    required this.child,
    this.showOnAppLaunch = true,
    this.showOnHomePage = true,
    this.delayBeforePrompt = const Duration(milliseconds: 500),
    this.showImmediately = true,
  }) : super(key: key);

  @override
  State<PWAInstallPrompt> createState() => _PWAInstallPromptState();
}

class _PWAInstallPromptState extends State<PWAInstallPrompt> {
  final PWAInstallService _pwaService = PWAInstallService();
  bool _hasShownPrompt = false;
  bool _isPromptVisible = false;

  @override
  void initState() {
    super.initState();
    _setupPromptLogic();
  }

  void _setupPromptLogic() {
    if (!kIsWeb) return;

    // Listen to installability changes
    _pwaService.installabilityStream.listen((isInstallable) {
      if (isInstallable && !_hasShownPrompt && mounted) {
        if (widget.showImmediately) {
          _schedulePrompt();
        }
      }
    });

    // Check if we should show prompt immediately on page load
    if (widget.showImmediately && _pwaService.shouldShowInstallPrompt() && !_hasShownPrompt) {
      _schedulePrompt();
    }

    // Also check after a short delay in case the service isn't ready yet
    Future.delayed(const Duration(milliseconds: 1000), () {
      if (mounted && widget.showImmediately && _pwaService.shouldShowInstallPrompt() && !_hasShownPrompt) {
        _schedulePrompt();
      }
    });
  }

  void _schedulePrompt() {
    if (_isPromptVisible || _hasShownPrompt) return;

    Future.delayed(widget.delayBeforePrompt, () {
      if (mounted && _pwaService.shouldShowInstallPrompt() && !_hasShownPrompt) {
        _showInstallPrompt();
      }
    });
  }

  Future<void> _showInstallPrompt() async {
    if (_isPromptVisible || _hasShownPrompt || !mounted) return;

    setState(() {
      _isPromptVisible = true;
      _hasShownPrompt = true;
    });

    await PWAInstallDialog.show(
      context,
      onInstallSuccess: () {
        debugPrint('🎉 PWA installed successfully!');
        setState(() {
          _isPromptVisible = false;
        });
      },
      onDismiss: () {
        debugPrint('📱 PWA install prompt dismissed');
        setState(() {
          _isPromptVisible = false;
        });
      },
    );

    if (mounted) {
      setState(() {
        _isPromptVisible = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
