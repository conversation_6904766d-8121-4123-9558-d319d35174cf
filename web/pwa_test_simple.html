<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KFT Fitness PWA Test</title>
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#3D5AFE">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            font-weight: 300;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            border-left: 4px solid #4CAF50;
        }
        .test-section h3 {
            margin-top: 0;
            color: #4CAF50;
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid #4CAF50;
        }
        .status.error {
            background: rgba(244, 67, 54, 0.2);
            border: 1px solid #f44336;
        }
        .status.warning {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid #FFC107;
        }
        button {
            background: linear-gradient(45deg, #3D5AFE, #536DFE);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(61, 90, 254, 0.3);
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        .install-prompt {
            text-align: center;
            margin: 20px 0;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4CAF50;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏋️ KFT Fitness PWA</h1>
        
        <div class="test-section">
            <h3>PWA Installation Status</h3>
            <div id="pwa-status" class="status warning">Checking PWA capabilities...</div>
            <div class="install-prompt">
                <button id="install-btn" onclick="installPWA()" disabled>
                    📱 Add to Home Screen
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>Service Worker Status</h3>
            <div id="sw-status" class="status warning">Checking Service Worker...</div>
            <button onclick="checkServiceWorker()">🔄 Refresh Status</button>
        </div>

        <div class="test-section">
            <h3>PWA Features</h3>
            <ul class="feature-list">
                <li>Offline functionality with intelligent caching</li>
                <li>Add to home screen for native app experience</li>
                <li>Fast loading with optimized service worker</li>
                <li>Professional installation prompts</li>
                <li>Push notification support</li>
                <li>Responsive design for all devices</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Quick Actions</h3>
            <button onclick="window.location.href='/'">🏠 Go to App</button>
            <button onclick="testOffline()">📡 Test Offline Mode</button>
            <button onclick="clearCache()">🗑️ Clear Cache</button>
        </div>
    </div>

    <script>
        let deferredPrompt;
        let isInstalled = false;

        // Check if app is already installed
        window.addEventListener('load', () => {
            checkPWAStatus();
            checkServiceWorker();
            
            // Check if running in standalone mode
            if (window.matchMedia('(display-mode: standalone)').matches) {
                isInstalled = true;
                updatePWAStatus('App is installed and running in standalone mode', 'success');
            }
        });

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            updatePWAStatus('PWA is installable', 'success');
            document.getElementById('install-btn').disabled = false;
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', () => {
            isInstalled = true;
            updatePWAStatus('PWA installed successfully!', 'success');
            document.getElementById('install-btn').disabled = true;
            deferredPrompt = null;
        });

        function checkPWAStatus() {
            const status = document.getElementById('pwa-status');
            
            if (isInstalled) {
                updatePWAStatus('App is installed', 'success');
                return;
            }

            if ('serviceWorker' in navigator && 'PushManager' in window) {
                if (deferredPrompt) {
                    updatePWAStatus('PWA is ready to install', 'success');
                    document.getElementById('install-btn').disabled = false;
                } else {
                    updatePWAStatus('PWA capabilities detected, waiting for install prompt', 'warning');
                }
            } else {
                updatePWAStatus('PWA not supported in this browser', 'error');
            }
        }

        function updatePWAStatus(message, type) {
            const status = document.getElementById('pwa-status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        function updateSWStatus(message, type) {
            const status = document.getElementById('sw-status');
            status.textContent = message;
            status.className = `status ${type}`;
        }

        async function installPWA() {
            if (!deferredPrompt) {
                alert('Install prompt not available. Try adding to home screen manually.');
                return;
            }

            const result = await deferredPrompt.prompt();
            console.log('Install prompt result:', result);

            if (result.outcome === 'accepted') {
                updatePWAStatus('Installation accepted', 'success');
            } else {
                updatePWAStatus('Installation declined', 'warning');
            }

            deferredPrompt = null;
            document.getElementById('install-btn').disabled = true;
        }

        async function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                try {
                    const registration = await navigator.serviceWorker.getRegistration();
                    if (registration) {
                        updateSWStatus(`Service Worker active (scope: ${registration.scope})`, 'success');
                    } else {
                        updateSWStatus('Service Worker not registered', 'error');
                    }
                } catch (error) {
                    updateSWStatus(`Service Worker error: ${error.message}`, 'error');
                }
            } else {
                updateSWStatus('Service Worker not supported', 'error');
            }
        }

        function testOffline() {
            // Simulate offline test by trying to fetch a non-existent resource
            fetch('/test-offline-fallback')
                .then(response => {
                    if (response.status === 503) {
                        alert('✅ Offline mode working! Service Worker provided fallback response.');
                    } else {
                        alert('⚠️ Unexpected response. Check service worker configuration.');
                    }
                })
                .catch(error => {
                    alert('❌ Network error: ' + error.message);
                });
        }

        async function clearCache() {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                alert('✅ Cache cleared successfully!');
                location.reload();
            } else {
                alert('❌ Cache API not supported');
            }
        }
    </script>
</body>
</html>
