<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Worker Test</title>
    <link rel="manifest" href="manifest.json">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 Service Worker & PWA Test</h1>
    
    <div id="status-container">
        <div id="sw-status" class="status">Checking Service Worker...</div>
        <div id="manifest-status" class="status">Checking Manifest...</div>
        <div id="install-status" class="status">Checking Install Prompt...</div>
    </div>

    <div>
        <button onclick="testServiceWorker()">Test Service Worker</button>
        <button onclick="testManifest()">Test Manifest</button>
        <button onclick="testInstallPrompt()">Test Install Prompt</button>
        <button onclick="forceInstall()">Force Install</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div id="log" class="log">
        <div>Service Worker test initialized...</div>
    </div>

    <script>
        let installPromptEvent = null;
        let startTime = Date.now();

        function log(message, type = 'info') {
            const logContainer = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const elapsed = Date.now() - startTime;
            
            const entry = document.createElement('div');
            entry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff';
            entry.textContent = `[${timestamp}] (+${elapsed}ms) ${message}`;
            
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        function updateStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        async function testServiceWorker() {
            log('Testing Service Worker...', 'info');
            
            if ('serviceWorker' in navigator) {
                try {
                    // Register service worker
                    const registration = await navigator.serviceWorker.register('/sw.js');
                    log(`✅ Service Worker registered: ${registration.scope}`, 'success');
                    updateStatus('sw-status', '✅ Service Worker: Registered', 'success');
                    
                    // Check existing registrations
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    log(`📋 Total registrations: ${registrations.length}`, 'info');
                    
                    registrations.forEach((reg, index) => {
                        log(`   ${index + 1}. Scope: ${reg.scope}`, 'info');
                        log(`      State: ${reg.active ? reg.active.state : 'inactive'}`, 'info');
                    });
                    
                    return true;
                } catch (error) {
                    log(`❌ Service Worker registration failed: ${error.message}`, 'error');
                    updateStatus('sw-status', '❌ Service Worker: Failed', 'error');
                    return false;
                }
            } else {
                log('❌ Service Worker not supported', 'error');
                updateStatus('sw-status', '❌ Service Worker: Not Supported', 'error');
                return false;
            }
        }

        async function testManifest() {
            log('Testing Manifest...', 'info');
            
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                log(`✅ Manifest loaded: ${manifest.name}`, 'success');
                log(`   Short name: ${manifest.short_name}`, 'info');
                log(`   Start URL: ${manifest.start_url}`, 'info');
                log(`   Display: ${manifest.display}`, 'info');
                log(`   Icons: ${manifest.icons ? manifest.icons.length : 0}`, 'info');
                
                updateStatus('manifest-status', '✅ Manifest: Valid', 'success');
                return true;
            } catch (error) {
                log(`❌ Manifest error: ${error.message}`, 'error');
                updateStatus('manifest-status', '❌ Manifest: Error', 'error');
                return false;
            }
        }

        function testInstallPrompt() {
            log('Testing Install Prompt...', 'info');
            
            if (installPromptEvent) {
                log('✅ Install prompt is available', 'success');
                updateStatus('install-status', '✅ Install: Ready', 'success');
                return true;
            } else {
                log('⚠️ Install prompt not available yet', 'warning');
                updateStatus('install-status', '⚠️ Install: Not Ready', 'warning');
                
                // Check PWA criteria
                const isHttps = location.protocol === 'https:' || location.hostname === 'localhost';
                log(`   HTTPS/Localhost: ${isHttps}`, isHttps ? 'success' : 'error');
                
                const hasManifest = document.querySelector('link[rel="manifest"]') !== null;
                log(`   Manifest linked: ${hasManifest}`, hasManifest ? 'success' : 'error');
                
                return false;
            }
        }

        function forceInstall() {
            if (installPromptEvent) {
                log('🚀 Triggering install prompt...', 'info');
                installPromptEvent.prompt();
                
                installPromptEvent.userChoice.then(choiceResult => {
                    if (choiceResult.outcome === 'accepted') {
                        log('🎉 User accepted install', 'success');
                        updateStatus('install-status', '✅ Install: Accepted', 'success');
                    } else {
                        log('❌ User dismissed install', 'warning');
                        updateStatus('install-status', '⚠️ Install: Dismissed', 'warning');
                    }
                });
            } else {
                log('❌ No install prompt available', 'error');
                alert('Install prompt not available. Check PWA requirements.');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>Log cleared...</div>';
            startTime = Date.now();
        }

        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            const elapsed = Date.now() - startTime;
            log(`🎯 beforeinstallprompt event received after ${elapsed}ms`, 'success');
            
            e.preventDefault();
            installPromptEvent = e;
            
            updateStatus('install-status', '✅ Install: Ready', 'success');
            
            // Auto-trigger after 2 seconds
            setTimeout(() => {
                if (confirm('PWA is installable! Install now?')) {
                    forceInstall();
                }
            }, 2000);
        });

        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            log('🎉 App installed successfully!', 'success');
            updateStatus('install-status', '✅ Install: Installed', 'success');
        });

        // Check if already installed
        function checkInstallStatus() {
            if (window.matchMedia('(display-mode: standalone)').matches) {
                log('📱 App is running in standalone mode (already installed)', 'success');
                updateStatus('install-status', '✅ Install: Already Installed', 'success');
            } else {
                log('🌐 App is running in browser mode', 'info');
            }
        }

        // Initialize tests
        window.addEventListener('load', async () => {
            log('Page loaded, running initial tests...', 'info');
            
            checkInstallStatus();
            
            const swOk = await testServiceWorker();
            const manifestOk = await testManifest();
            testInstallPrompt();
            
            if (swOk && manifestOk) {
                log('✅ Basic PWA requirements met', 'success');
                log('⏳ Waiting for beforeinstallprompt event...', 'info');
            } else {
                log('❌ Some PWA requirements not met', 'error');
            }
        });

        // Log any errors
        window.addEventListener('error', (e) => {
            log(`❌ JavaScript error: ${e.message}`, 'error');
        });

        // Log unhandled promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            log(`❌ Unhandled promise rejection: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
